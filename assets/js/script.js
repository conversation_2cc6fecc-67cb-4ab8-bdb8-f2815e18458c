// Optimized Performance utilities for smooth scrolling
const throttle = (func, limit = 16) => {
    let lastFunc;
    let lastRan;
    return function() {
        const context = this;
        const args = arguments;
        if (!lastRan) {
            func.apply(context, args);
            lastRan = Date.now();
        } else {
            clearTimeout(lastFunc);
            lastFunc = setTimeout(function() {
                if ((Date.now() - lastRan) >= limit) {
                    func.apply(context, args);
                    lastRan = Date.now();
                }
            }, limit - (Date.now() - lastRan));
        }
    }
};

const debounce = (func, wait = 100, immediate = false) => {
    let timeout;
    return function() {
        const context = this, args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
};

// RequestAnimationFrame-based smooth scroll utility
const smoothScrollRAF = (targetPosition, duration = 800) => {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);

        // Optimized easing function
        const easeInOutCubic = progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        window.scrollTo(0, startPosition + distance * easeInOutCubic);

        if (timeElapsed < duration) {
            requestAnimationFrame(animation);
        }
    }

    requestAnimationFrame(animation);
};

// Optimized smooth scrolling with better easing
// Ultra-smooth optimized scrolling with hardware acceleration
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (!element) return;

    const targetPosition = element.getBoundingClientRect().top + window.pageYOffset - 80;
    smoothScrollRAF(targetPosition, 600);
}

// Enhanced smooth scroll to specific client in Client Results section
function scrollToClientResults(clientId = null) {
    console.log('🔍 scrollToClientResults function called with clientId:', clientId);

    const clientResultsSection = document.getElementById('client-results');
    console.log('📍 Client Results Section:', clientResultsSection);

    if (clientResultsSection) {
        let targetElement = clientResultsSection;
        let targetPosition = clientResultsSection.getBoundingClientRect().top + window.pageYOffset - 80;

        // If specific client ID provided, target that client's card
        if (clientId) {
            const specificClientCard = document.getElementById(`client-${clientId}`);
            console.log(`🎯 Looking for specific client: client-${clientId}`, specificClientCard);

            if (specificClientCard) {
                targetElement = specificClientCard;
                targetPosition = specificClientCard.getBoundingClientRect().top + window.pageYOffset - 100;
                console.log('✅ Found specific client card, targeting it');

                // Clear any previous highlights
                document.querySelectorAll('.result-card').forEach(card => {
                    card.classList.remove('highlighted-client');
                });

                // Highlight the specific client card
                specificClientCard.classList.add('highlighted-client');
                console.log('🎨 Applied highlight to specific client');

                // Remove highlight after 3 seconds
                setTimeout(() => {
                    specificClientCard.classList.remove('highlighted-client');
                    console.log('🎨 Removed highlight from client');
                }, 3000);
            } else {
                console.warn(`⚠️ Specific client card not found: client-${clientId}, falling back to section`);
            }
        }

        console.log('📏 Target Position:', targetPosition);
        console.log('📏 Current Position:', window.pageYOffset);

        // Primary scroll method using RAF
        try {
            smoothScrollRAF(targetPosition, 800);
            console.log('✅ RAF scroll initiated');
        } catch (error) {
            console.warn('⚠️ RAF scroll failed, using fallback:', error);
            // Fallback to native smooth scroll
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Track the interaction
        if (typeof trackEvent === 'function') {
            trackEvent('view_client_results', {
                source: 'testimonial_button',
                client: clientId || 'general'
            });
        }

        console.log('✅ Scroll initiated successfully');
    } else {
        console.error('❌ Client Results section not found!');
    }
}

// Make function globally available
window.scrollToClientResults = scrollToClientResults;

// Test function to verify individual client targeting functionality
function testScrollToClientResults() {
    console.log('🧪 Testing scrollToClientResults functionality with client targeting...');

    // Check if function exists
    if (typeof scrollToClientResults === 'function') {
        console.log('✅ scrollToClientResults function exists');
    } else {
        console.error('❌ scrollToClientResults function not found');
        return false;
    }

    // Check if target element exists
    const clientResultsSection = document.getElementById('client-results');
    if (clientResultsSection) {
        console.log('✅ Client Results section found');
    } else {
        console.error('❌ Client Results section not found');
        return false;
    }

    // Check if individual client cards exist
    const clientIds = ['priya-sharma', 'rajesh-kumar'];
    const clientNames = ['Vinay pratap', 'Bhudil viyas'];

    clientIds.forEach((clientId, index) => {
        const clientCard = document.getElementById(`client-${clientId}`);
        if (clientCard) {
            console.log(`✅ Found client card for ${clientNames[index]}: client-${clientId}`);
        } else {
            console.error(`❌ Client card not found for ${clientNames[index]}: client-${clientId}`);
        }
    });

    // Check if buttons exist
    const buttons = document.querySelectorAll('.testimonial-details-btn');
    console.log(`✅ Found ${buttons.length} testimonial buttons`);

    if (buttons.length === 0) {
        console.error('❌ No testimonial buttons found');
        return false;
    }

    // Test individual client targeting
    console.log('🎯 Testing individual client targeting...');
    clientIds.forEach((clientId, index) => {
        console.log(`🔍 Testing scroll to ${clientNames[index]} (${clientId})`);
    });

    console.log('🎉 All components verified successfully with client targeting!');
    return true;
}

// Make test function globally available
window.testScrollToClientResults = testScrollToClientResults;

// Optimized parallax scrolling with requestAnimationFrame
function initParallax() {
    let ticking = false;
    let scrollY = 0;

    const parallaxElements = document.querySelectorAll('.hero::before, .hero::after');
    const heroElements = document.querySelectorAll('.hero-placeholder, .hero-content');

    function updateParallax() {
        // Parallax for hero background elements
        parallaxElements.forEach((element, index) => {
            const speed = 0.3 + (index * 0.1); // Reduced speed for smoother effect
            const yPos = -(scrollY * speed);
            const rotation = scrollY * 0.005; // Reduced rotation for smoother effect

            // Use transform3d for hardware acceleration
            element.style.transform = `translate3d(0, ${yPos}px, 0) rotateX(${rotation}deg)`;
        });

        // Parallax for hero content elements
        heroElements.forEach((element, index) => {
            const speed = 0.1 + (index * 0.05);
            const yPos = scrollY * speed;
            element.style.transform = `translate3d(0, ${yPos}px, 0)`;
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    // Throttled scroll listener for better performance
    const handleScroll = throttle(() => {
        scrollY = window.pageYOffset;
        requestTick();
    }, 16); // ~60fps

    window.addEventListener('scroll', handleScroll, { passive: true });
}

// Advanced animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize parallax
    initParallax();

    // Initialize staggered animations
    initStaggeredAnimations();

    // Initialize 3D tilt effects
    init3DTiltEffects();

    // Initialize touch gestures for mobile
    initTouchGestures();

    // Initialize particle effects
    initParticleEffects();

    // Initialize enhanced interactions
    initEnhancedInteractions();

    // Mobile navigation toggle with animation
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            hamburger.classList.toggle('active');

            // Add ripple effect
            createRippleEffect(hamburger);
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
                navLinks.classList.remove('active');
                hamburger.classList.remove('active');
            }
        });
    }

    // FAQ accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            // Close other open items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Toggle current item
            item.classList.toggle('active');
        });
    });

    // Optimized scroll effects with performance improvements
    let scrollTicking = false;
    let lastScrollY = 0;

    const navbar = document.querySelector('.navbar');
    const scrollIndicator = document.getElementById('scrollIndicator');
    const maxScroll = document.body.scrollHeight - window.innerHeight;

    function updateScrollEffects() {
        const scrolled = window.pageYOffset;
        const scrollPercentage = Math.min((scrolled / maxScroll) * 100, 100);

        // Only update if scroll position changed significantly
        if (Math.abs(scrolled - lastScrollY) > 1) {
            // Update navbar background with smooth transition
            if (navbar) {
                if (scrolled > 100) {
                    navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                    navbar.style.backdropFilter = 'blur(20px)';
                } else {
                    navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                    navbar.style.backdropFilter = 'blur(10px)';
                }
            }

            // Update scroll indicator efficiently
            if (scrollIndicator) {
                scrollIndicator.style.transform = `scaleX(${scrollPercentage / 100})`;
                scrollIndicator.style.transformOrigin = 'left';
            }

            lastScrollY = scrolled;
        }

        scrollTicking = false;
    }

    function requestScrollTick() {
        if (!scrollTicking) {
            requestAnimationFrame(updateScrollEffects);
            scrollTicking = true;
        }
    }

    // Optimized scroll listener
    const handleScrollEffects = throttle(() => {
        requestScrollTick();
    }, 16);

    window.addEventListener('scroll', handleScrollEffects, { passive: true });

    // Optimized Intersection Observer for smooth scroll animations
    const observerOptions = {
        threshold: [0, 0.1, 0.2, 0.3], // Multiple thresholds for smoother detection
        rootMargin: '0px 0px -30px 0px'
    };

    const animationObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && entry.intersectionRatio > 0.1) {
                const element = entry.target;

                // Prevent multiple animations
                if (element.classList.contains('animate-in')) return;

                const animationType = element.dataset.animation || 'slideInUp';
                const delay = element.dataset.delay || '0s';

                // Use requestAnimationFrame for smooth animation start
                requestAnimationFrame(() => {
                    element.style.animation = `${animationType} 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${delay} forwards`;
                    element.classList.add('animate-in');
                });

                // Unobserve after animation to improve performance
                animationObserver.unobserve(element);
            }
        });
    }, observerOptions);

    // Setup elements for optimized animations
    const animateElements = document.querySelectorAll('.problem-item, .benefit-card, .testimonial-card, .feature');
    const animations = ['slideInLeft', 'slideInRight', 'slideInUp', 'scaleIn'];

    animateElements.forEach((el, index) => {
        // Prepare element for animation
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.willChange = 'transform, opacity';

        // Stagger animations within groups
        const groupIndex = Math.floor(index / 3);
        const itemIndex = index % 3;
        el.dataset.delay = `${itemIndex * 0.1}s`;
        el.dataset.animation = animations[groupIndex % animations.length];

        animationObserver.observe(el);
    });
});

// Staggered animations for grid items
function initStaggeredAnimations() {
    const grids = document.querySelectorAll('.benefits-grid, .testimonials-grid, .problem-grid');

    grids.forEach(grid => {
        const items = grid.children;
        Array.from(items).forEach((item, index) => {
            item.style.animationDelay = `${index * 0.15}s`;
            item.classList.add('card-3d');
        });
    });
}

// Optimized 3D tilt effects with performance improvements
function init3DTiltEffects() {
    const cards = document.querySelectorAll('.card-3d, .benefit-card, .testimonial-card, .problem-item');
    let tiltAnimationId = null;

    cards.forEach(card => {
        let isHovering = false;
        let currentRotateX = 0;
        let currentRotateY = 0;
        let targetRotateX = 0;
        let targetRotateY = 0;

        // Prepare card for 3D transforms
        card.style.willChange = 'transform';
        card.style.transformStyle = 'preserve-3d';

        function updateTilt() {
            // Smooth interpolation for fluid animation
            currentRotateX += (targetRotateX - currentRotateX) * 0.1;
            currentRotateY += (targetRotateY - currentRotateY) * 0.1;

            if (isHovering || Math.abs(currentRotateX) > 0.1 || Math.abs(currentRotateY) > 0.1) {
                card.style.transform = `perspective(1000px) rotateX(${currentRotateX}deg) rotateY(${currentRotateY}deg) translateZ(${isHovering ? 20 : 0}px)`;
                tiltAnimationId = requestAnimationFrame(updateTilt);
            } else {
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
                card.style.willChange = 'auto'; // Reset will-change when not animating
            }
        }

        const handleMouseMove = throttle((e) => {
            if (!isHovering) return;

            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            // Reduced rotation intensity for smoother effect
            targetRotateX = (y - centerY) / 15;
            targetRotateY = (centerX - x) / 15;

            if (!tiltAnimationId) {
                card.style.willChange = 'transform';
                updateTilt();
            }
        }, 16);

        card.addEventListener('mouseenter', () => {
            isHovering = true;
        });

        card.addEventListener('mousemove', handleMouseMove);

        card.addEventListener('mouseleave', () => {
            isHovering = false;
            targetRotateX = 0;
            targetRotateY = 0;

            if (!tiltAnimationId) {
                updateTilt();
            }
        });
    });
}

// Optimized touch gestures for mobile with better performance
function initTouchGestures() {
    let startY = 0;
    let startX = 0;
    let touchTicking = false;

    const parallaxElements = document.querySelectorAll('.hero-placeholder, .benefit-icon');

    function updateTouchParallax(diffY, diffX) {
        parallaxElements.forEach(el => {
            const speed = 0.05; // Reduced for smoother effect
            el.style.transform = `translate3d(${diffX * speed}px, ${diffY * speed}px, 0)`;
        });
        touchTicking = false;
    }

    function requestTouchTick(diffY, diffX) {
        if (!touchTicking) {
            requestAnimationFrame(() => updateTouchParallax(diffY, diffX));
            touchTicking = true;
        }
    }

    const handleTouchStart = (e) => {
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
    };

    const handleTouchMove = throttle((e) => {
        if (!startY || !startX) return;

        const currentY = e.touches[0].clientY;
        const currentX = e.touches[0].clientX;
        const diffY = startY - currentY;
        const diffX = startX - currentX;

        requestTouchTick(diffY, diffX);
    }, 16);

    const handleTouchEnd = () => {
        startY = 0;
        startX = 0;

        // Reset parallax elements smoothly
        parallaxElements.forEach(el => {
            el.style.transition = 'transform 0.3s ease-out';
            el.style.transform = 'translate3d(0, 0, 0)';

            setTimeout(() => {
                el.style.transition = '';
            }, 300);
        });
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
}

// Ripple effect for buttons
function createRippleEffect(element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    ripple.classList.add('ripple');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Optimized particle effects with performance controls
function initParticleEffects() {
    // Skip particles on mobile or low-performance devices
    if (window.innerWidth < 768 || navigator.hardwareConcurrency < 4) {
        return;
    }

    const particleContainer = document.createElement('div');
    particleContainer.className = 'particle-container';
    document.body.appendChild(particleContainer);

    let particleCount = 0;
    const maxParticles = 8; // Limit concurrent particles

    function createParticle() {
        if (particleCount >= maxParticles) return;

        const particle = document.createElement('div');
        particle.className = 'particle';
        particleCount++;

        // Random position and properties
        const size = Math.random() * 3 + 1; // Smaller particles
        const x = Math.random() * window.innerWidth;
        const duration = Math.random() * 2 + 3; // Longer duration, fewer particles

        particle.style.cssText = `
            position: fixed;
            width: ${size}px;
            height: ${size}px;
            background: radial-gradient(circle, rgba(255, 107, 53, 0.4), rgba(255, 215, 0, 0.2));
            border-radius: 50%;
            left: ${x}px;
            bottom: -10px;
            pointer-events: none;
            z-index: 1;
            will-change: transform;
            animation: floatUp ${duration}s linear forwards;
        `;

        particleContainer.appendChild(particle);

        setTimeout(() => {
            if (particle.parentNode) {
                particle.remove();
                particleCount--;
            }
        }, duration * 1000);
    }

    // Create particles less frequently for better performance
    const particleInterval = setInterval(createParticle, 3000);

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        clearInterval(particleInterval);
    });
}

// Optimized enhanced interactions
function initEnhancedInteractions() {
    // Optimized magnetic effect for buttons
    const buttons = document.querySelectorAll('.cta-primary, .cta-secondary');

    buttons.forEach(button => {
        let magneticAnimationId = null;
        let currentX = 0;
        let currentY = 0;
        let targetX = 0;
        let targetY = 0;
        let isHovering = false;

        button.style.willChange = 'transform';

        function updateMagnetic() {
            currentX += (targetX - currentX) * 0.15;
            currentY += (targetY - currentY) * 0.15;

            if (isHovering || Math.abs(currentX) > 0.1 || Math.abs(currentY) > 0.1) {
                button.style.transform = `translate3d(${currentX}px, ${currentY}px, 0) scale(${isHovering ? 1.05 : 1})`;
                magneticAnimationId = requestAnimationFrame(updateMagnetic);
            } else {
                button.style.transform = 'translate3d(0px, 0px, 0) scale(1)';
                button.style.willChange = 'auto';
                magneticAnimationId = null;
            }
        }

        const handleMouseMove = throttle((e) => {
            if (!isHovering) return;

            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            const distance = Math.sqrt(x * x + y * y);
            const maxDistance = 80; // Reduced for subtler effect

            if (distance < maxDistance) {
                const force = (maxDistance - distance) / maxDistance;
                targetX = x * force * 0.2; // Reduced intensity
                targetY = y * force * 0.2;

                if (!magneticAnimationId) {
                    button.style.willChange = 'transform';
                    updateMagnetic();
                }
            }
        }, 16);

        button.addEventListener('mouseenter', () => {
            isHovering = true;
        });

        button.addEventListener('mousemove', handleMouseMove);

        button.addEventListener('mouseleave', () => {
            isHovering = false;
            targetX = 0;
            targetY = 0;

            if (!magneticAnimationId) {
                updateMagnetic();
            }
        });
    });

    // Optimized scroll-based animations for sections
    const sections = document.querySelectorAll('section');

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && entry.intersectionRatio > 0.1) {
                const section = entry.target;

                if (!section.classList.contains('section-animated')) {
                    requestAnimationFrame(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0px)';
                        section.classList.add('section-animated');
                    });

                    // Unobserve after animation
                    sectionObserver.unobserve(section);
                }
            }
        });
    }, {
        threshold: [0.1, 0.2],
        rootMargin: '0px 0px -50px 0px'
    });

    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        section.style.transitionDelay = `${Math.min(index * 0.05, 0.3)}s`; // Cap delay
        section.style.willChange = 'transform, opacity';

        sectionObserver.observe(section);
    });

    // Optimized cursor trail effect (desktop only)
    if (window.innerWidth > 768 && !('ontouchstart' in window)) {
        let trail = [];
        const trailLength = 6; // Reduced for better performance
        let trailTicking = false;

        function updateTrail(x, y) {
            trail.push({ x, y, time: Date.now() });

            if (trail.length > trailLength) {
                trail.shift();
            }

            // Remove old trail elements efficiently
            const existingTrails = document.querySelectorAll('.cursor-trail');
            existingTrails.forEach(el => el.remove());

            // Create new trail elements with better performance
            const fragment = document.createDocumentFragment();

            trail.forEach((point, index) => {
                const trailElement = document.createElement('div');
                trailElement.className = 'cursor-trail';
                const size = (index + 1) * 1.5; // Smaller trail
                const opacity = (index + 1) / trailLength * 0.6; // Reduced opacity

                trailElement.style.cssText = `
                    position: fixed;
                    width: ${size}px;
                    height: ${size}px;
                    background: radial-gradient(circle, rgba(255, 107, 53, ${opacity}), transparent);
                    border-radius: 50%;
                    left: ${point.x - size/2}px;
                    top: ${point.y - size/2}px;
                    pointer-events: none;
                    z-index: 9999;
                    will-change: transform;
                `;

                fragment.appendChild(trailElement);
            });

            document.body.appendChild(fragment);
            trailTicking = false;
        }

        function requestTrailTick(x, y) {
            if (!trailTicking) {
                requestAnimationFrame(() => updateTrail(x, y));
                trailTicking = true;
            }
        }

        const handleMouseMove = throttle((e) => {
            requestTrailTick(e.clientX, e.clientY);
        }, 32); // Reduced frequency for better performance

        document.addEventListener('mousemove', handleMouseMove);
    }
}

// WhatsApp integration
function openWhatsApp() {
    const phoneNumber = '918504855728'; // Replace with actual phone number
    const message = encodeURIComponent('Hi! I\'m interested in the Coach Natru transformation program. Can you provide more details?');
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappURL, '_blank');
}

// Phone call integration
function openCall() {
    window.location.href = 'tel:+918504855728'; // Replace with actual phone number
}

// Form validation (if you add a contact form later)
function validateForm(formData) {
    const errors = [];
    
    if (!formData.name || formData.name.trim().length < 2) {
        errors.push('Name must be at least 2 characters long');
    }
    
    if (!formData.email || !isValidEmail(formData.email)) {
        errors.push('Please enter a valid email address');
    }
    
    if (!formData.phone || formData.phone.trim().length < 10) {
        errors.push('Please enter a valid phone number');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Analytics tracking (placeholder - replace with actual analytics)
function trackEvent(eventName, eventData = {}) {
    // Google Analytics 4 example
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, eventData);
    }
    
    // Facebook Pixel example
    if (typeof fbq !== 'undefined') {
        fbq('track', eventName, eventData);
    }
    
    console.log('Event tracked:', eventName, eventData);
}

// Track CTA clicks
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('cta-primary')) {
        trackEvent('cta_click', {
            button_text: e.target.textContent.trim(),
            page_section: e.target.closest('section')?.className || 'unknown'
        });
    }
    
    if (e.target.classList.contains('cta-secondary')) {
        trackEvent('secondary_cta_click', {
            button_text: e.target.textContent.trim()
        });
    }
});

// Page load tracking
window.addEventListener('load', function() {
    trackEvent('page_view', {
        page_title: document.title,
        page_url: window.location.href
    });
});

// Scroll depth tracking
let maxScrollDepth = 0;
window.addEventListener('scroll', function() {
    const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
        
        // Track milestone scroll depths
        if (maxScrollDepth >= 25 && maxScrollDepth < 50) {
            trackEvent('scroll_depth', { depth: '25%' });
        } else if (maxScrollDepth >= 50 && maxScrollDepth < 75) {
            trackEvent('scroll_depth', { depth: '50%' });
        } else if (maxScrollDepth >= 75 && maxScrollDepth < 90) {
            trackEvent('scroll_depth', { depth: '75%' });
        } else if (maxScrollDepth >= 90) {
            trackEvent('scroll_depth', { depth: '90%' });
        }
    }
});

// Performance optimization - lazy loading for images (when you add real images)
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', lazyLoadImages);

// Add loading states for buttons
function addLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    button.disabled = true;
    
    // Remove loading state after 2 seconds (adjust as needed)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}

// Enhanced CTA click handlers with loading states
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('cta-primary') || e.target.classList.contains('cta-secondary')) {
        addLoadingState(e.target);
    }
});

// Contact form handling
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('name').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                goal: document.getElementById('goal').value
            };

            const errors = validateForm(formData);

            if (errors.length > 0) {
                alert('Please fix the following errors:\n' + errors.join('\n'));
                return;
            }

            // Add loading state to submit button
            const submitBtn = contactForm.querySelector('.form-submit-btn');
            addLoadingState(submitBtn);

            // Track form submission
            trackEvent('form_submission', {
                goal: formData.goal,
                source: 'landing_page'
            });

            // Simulate form submission (replace with actual form handling)
            setTimeout(() => {
                // Create WhatsApp message with form data
                const message = encodeURIComponent(
                    `Hi! I filled out the consultation form on your website.\n\n` +
                    `Name: ${formData.name}\n` +
                    `Phone: ${formData.phone}\n` +
                    `Email: ${formData.email}\n` +
                    `Goal: ${formData.goal}\n\n` +
                    `I'd like to schedule my free consultation. When would be a good time to talk?`
                );

                const phoneNumber = '918504855728'; // Replace with actual phone number
                const whatsappURL = `https://wa.me/${phoneNumber}?text=${message}`;
                window.open(whatsappURL, '_blank');

                // Show success message
                alert('Thank you! Your consultation request has been sent. We\'ll contact you within 24 hours.');

                // Reset form
                contactForm.reset();
            }, 2000);
        });
    }
});

// Performance monitoring and optimization
function initPerformanceOptimizations() {
    // Reduce motion for users who prefer it
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01s');
        document.documentElement.style.setProperty('--transition-duration', '0.01s');

        // Disable particle effects and cursor trail
        const particleContainer = document.querySelector('.particle-container');
        if (particleContainer) {
            particleContainer.style.display = 'none';
        }

        // Disable 3D transforms
        const cards = document.querySelectorAll('.card-3d, .benefit-card, .testimonial-card');
        cards.forEach(card => {
            card.style.transform = 'none';
            card.style.transition = 'none';
        });
    }

    // Monitor performance and adjust accordingly
    let frameCount = 0;
    let lastTime = performance.now();

    function checkPerformance() {
        frameCount++;
        const currentTime = performance.now();

        if (currentTime - lastTime >= 1000) {
            const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

            // If FPS is low, reduce effects
            if (fps < 30) {
                console.log('Low FPS detected, reducing effects');

                // Disable particle effects
                const particleContainer = document.querySelector('.particle-container');
                if (particleContainer) {
                    particleContainer.style.display = 'none';
                }

                // Reduce animation complexity
                document.documentElement.style.setProperty('--animation-duration', '0.3s');
                document.documentElement.style.setProperty('--transition-duration', '0.3s');
            }

            frameCount = 0;
            lastTime = currentTime;
        }

        requestAnimationFrame(checkPerformance);
    }

    // Start performance monitoring after page load
    setTimeout(() => {
        requestAnimationFrame(checkPerformance);
    }, 2000);
}

// Scroll to Top Button Functionality
function initScrollToTop() {
    const scrollToTopBtn = document.getElementById('scrollToTop');

    if (!scrollToTopBtn) return;

    // Show/hide button based on scroll position
    const toggleScrollButton = throttle(() => {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('visible');
        } else {
            scrollToTopBtn.classList.remove('visible');
        }
    }, 100);

    // Smooth scroll to top
    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    // Event listeners
    window.addEventListener('scroll', toggleScrollButton, { passive: true });
    scrollToTopBtn.addEventListener('click', scrollToTop);

    // Initial check
    toggleScrollButton();
}

// Navigation Enhancement
function initNavigationEnhancements() {
    // Only target anchor links (links that start with #) for smooth scrolling
    const anchorLinks = document.querySelectorAll('.nav-links a[href^="#"]:not(.cta-nav)');
    const allNavLinks = document.querySelectorAll('.nav-links a');
    const sections = document.querySelectorAll('section[id]');
    const navbar = document.querySelector('.navbar');

    // Optimized active state with RAF
    let ticking = false;
    const updateActiveNavigation = () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                let current = '';
                const scrollY = window.pageYOffset;

                // Update navbar appearance on scroll
                if (scrollY > 100) {
                    navbar?.classList.add('scrolled');
                } else {
                    navbar?.classList.remove('scrolled');
                }

                // Use getBoundingClientRect for better performance
                sections.forEach(section => {
                    const rect = section.getBoundingClientRect();
                    if (rect.top <= 200 && rect.bottom >= 200) {
                        current = section.getAttribute('id');
                    }
                });

                // Only update active states for anchor links on the same page
                anchorLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });

                ticking = false;
            });
            ticking = true;
        }
    };

    // Enhanced hover effects for ALL navigation links
    allNavLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = '';
            }
        });
    });

    // ONLY add smooth scroll behavior to anchor links (links starting with #)
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);

            // Close mobile menu if open
            const navLinksContainer = document.querySelector('.nav-links');
            if (navLinksContainer.classList.contains('active')) {
                navLinksContainer.classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            }
        });
    });

    // Add mobile menu close functionality for page navigation links
    const pageNavLinks = document.querySelectorAll('.nav-links a:not([href^="#"])');
    console.log(`🔗 Found ${pageNavLinks.length} page navigation links`);

    pageNavLinks.forEach((link, index) => {
        console.log(`🔗 Page nav link ${index + 1}: ${link.href}`);
        link.addEventListener('click', function() {
            console.log(`🖱️ Clicked page navigation link: ${this.href}`);

            // Close mobile menu if open when navigating to other pages
            const navLinksContainer = document.querySelector('.nav-links');
            if (navLinksContainer.classList.contains('active')) {
                navLinksContainer.classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
                console.log('📱 Mobile menu closed');
            }

            // Allow normal navigation to proceed
            console.log('✅ Navigation proceeding normally');
        });
    });

    // Use passive event listener for optimal scroll performance
    window.addEventListener('scroll', updateActiveNavigation, { passive: true });

    // Initial call
    updateActiveNavigation();
}

// Enhanced Image Loading with Smooth Transitions
function enhanceImageLoading() {
    const images = document.querySelectorAll('img');

    images.forEach(img => {
        img.classList.add('image-loading');

        if (img.complete) {
            img.classList.remove('image-loading');
            img.classList.add('image-loaded');
        } else {
            img.addEventListener('load', () => {
                img.classList.remove('image-loading');
                img.classList.add('image-loaded');
            });
        }
    });
}

// Optimized Scroll-Triggered Animations with RAF
function initScrollAnimations() {
    const observerOptions = {
        threshold: [0.1, 0.25],
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Use RAF for smooth animation triggering
                requestAnimationFrame(() => {
                    entry.target.classList.add('animate');

                    // Optimized staggered animation for child elements
                    const children = entry.target.querySelectorAll('.stagger-animation');
                    children.forEach((child, index) => {
                        requestAnimationFrame(() => {
                            setTimeout(() => {
                                child.classList.add('animate');
                            }, index * 50); // Reduced delay for smoother effect
                        });
                    });
                });

                // Stop observing once animated to improve performance
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.fade-in-up, .stagger-animation, .section-reveal').forEach(el => {
        observer.observe(el);
    });
}

// Enhanced Button Interactions with Magnetic Effect
function addMagneticEffect() {
    const magneticButtons = document.querySelectorAll('.cta-primary, .cta-secondary, .magnetic-button');

    magneticButtons.forEach(button => {
        button.addEventListener('mousemove', (e) => {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            button.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translate(0, 0)';
        });
    });
}

// Enhanced Typography and Readability
function enhanceTypography() {
    // Add interactive element classes for better hover effects
    document.querySelectorAll('.pricing-card, .testimonial-card, .feature-item, .benefit-card').forEach(el => {
        el.classList.add('interactive-element');
    });

    // Add fade-in-up class to sections for scroll animations
    document.querySelectorAll('section').forEach(section => {
        section.classList.add('fade-in-up');
    });

    // Add stagger animation to grid items
    document.querySelectorAll('.features-grid > *, .benefits-grid > *, .testimonials-grid > *').forEach(item => {
        item.classList.add('stagger-animation');
    });
}

// Enhanced Performance Optimizations
function enhancePerformance() {
    // Optimize images with lazy loading attributes
    document.querySelectorAll('img').forEach(img => {
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }
    });

    // Add will-change optimization for animated elements
    document.querySelectorAll('.cta-primary, .cta-secondary, .card, .pricing-card').forEach(el => {
        el.style.willChange = 'transform';
    });
}

// Enhanced smooth scrolling initialization
function initOptimizedSmoothScrolling() {
    // Use passive event listeners for better performance
    document.addEventListener('click', function(e) {
        const target = e.target.closest('a[href^="#"]');
        if (!target) return;

        e.preventDefault();
        const targetId = target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
            const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - 80;
            smoothScrollRAF(targetPosition, 600);
        }
    }, { passive: false });
}

// Initialize testimonial button functionality with client targeting
function initTestimonialButtons() {
    console.log('🔧 Initializing testimonial buttons...');

    const testimonialButtons = document.querySelectorAll('.testimonial-details-btn');
    console.log('🔍 Found testimonial buttons:', testimonialButtons.length);

    // Map button indices to client IDs
    const clientMapping = ['priya-sharma', 'rajesh-kumar'];

    testimonialButtons.forEach((button, index) => {
        const clientId = clientMapping[index];
        console.log(`🔘 Setting up button ${index + 1} for client: ${clientId}`);

        // Add data attribute for client identification
        button.setAttribute('data-client', clientId);

        // Add click event listener as backup to onclick
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`🖱️ Button ${index + 1} clicked via event listener for client: ${clientId}`);
            scrollToClientResults(clientId);
        });

        // Add visual feedback
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    console.log('✅ Testimonial buttons initialized successfully with client targeting');
}

// Initialize all enhancements with optimized loading
document.addEventListener('DOMContentLoaded', function() {
    // Core performance optimizations first
    initPerformanceOptimizations();
    initOptimizedSmoothScrolling();

    // Navigation and scroll features
    initScrollToTop();
    initNavigationEnhancements();
    initTestimonialButtons(); // Add testimonial button initialization

    // Visual enhancements
    enhanceImageLoading();
    initScrollAnimations();
    addMagneticEffect();
    enhanceTypography();
    enhancePerformance();

    console.log('🚀 Ultra-smooth landing page loaded with optimized performance!');
});
